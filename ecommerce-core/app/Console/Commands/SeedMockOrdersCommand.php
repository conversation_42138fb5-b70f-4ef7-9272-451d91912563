<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\User;
use App\Models\OrderStatus;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Faker\Factory as Faker;

class SeedMockOrdersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seed:mock-orders
                            {count=10 : Number of orders to create}
                            {--products-per-order=5 : Maximum number of products per order}
                            {--min-products=1 : Minimum number of products per order}
                            {--with-tracking : Include tracking numbers for orders}
                            {--status= : Specific order status to use}
                            {--recent : Create only recent orders (last month)}
                            {--user-id= : Create orders for specific user ID}
                            {--categories=* : Specific product categories to include}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed mock orders with randomized order products';

    /**
     * Faker instance
     *
     * @var \Faker\Generator
     */
    protected $faker;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->faker = Faker::create();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $count = (int) $this->argument('count');
        $maxProductsPerOrder = (int) $this->option('products-per-order');
        $minProductsPerOrder = (int) $this->option('min-products');
        $withTracking = $this->option('with-tracking');
        $specificStatus = $this->option('status');
        $recentOnly = $this->option('recent');
        $specificUserId = $this->option('user-id');
        $categories = $this->option('categories');

        $this->info("Creating {$count} mock orders with {$minProductsPerOrder}-{$maxProductsPerOrder} products each...");

        if ($withTracking) {
            $this->info("Including tracking numbers for all orders.");
        }

        if ($specificStatus) {
            $this->info("Using order status: {$specificStatus}");
        }

        if ($recentOnly) {
            $this->info("Creating only recent orders (last month).");
        }

        if ($specificUserId) {
            $this->info("Creating orders for user ID: {$specificUserId}");
        }

        if (!empty($categories)) {
            $this->info("Including products from categories: " . implode(', ', $categories));
        }

        // Check if required data exists
        if (!$this->validateRequiredData()) {
            return Command::FAILURE;
        }

        $progressBar = $this->output->createProgressBar($count);
        $progressBar->start();

        DB::transaction(function () use ($count, $maxProductsPerOrder, $minProductsPerOrder, $progressBar, $withTracking, $specificStatus, $recentOnly, $specificUserId, $categories) {
            for ($i = 0; $i < $count; $i++) {
                $this->createMockOrder(
                    $minProductsPerOrder,
                    $maxProductsPerOrder,
                    $withTracking,
                    $specificStatus,
                    $recentOnly,
                    $specificUserId,
                    $categories
                );
                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine();
        $this->info("Successfully created {$count} mock orders!");

        return Command::SUCCESS;
    }

    /**
     * Validate that required data exists
     *
     * @return bool
     */
    protected function validateRequiredData(): bool
    {
        $users = User::count();
        $products = Product::count();
        $orderStatuses = OrderStatus::count();

        if ($users === 0) {
            $this->error('No users found. Please create users first.');
            return false;
        }

        if ($products === 0) {
            $this->error('No products found. Please create products first.');
            return false;
        }

        if ($orderStatuses === 0) {
            $this->error('No order statuses found. Please create order statuses first.');
            return false;
        }

        $this->info("Found {$users} users, {$products} products, and {$orderStatuses} order statuses.");
        return true;
    }

    /**
     * Create a single mock order with random products
     *
     * @param int $minProducts
     * @param int $maxProducts
     * @param bool $withTracking
     * @param string|null $specificStatus
     * @param bool $recentOnly
     * @param string|null $specificUserId
     * @param array $categories
     * @return void
     */
    protected function createMockOrder(
        int $minProducts,
        int $maxProducts,
        bool $withTracking = false,
        ?string $specificStatus = null,
        bool $recentOnly = false,
        ?string $specificUserId = null,
        array $categories = []
    ): void {
        // Get user
        if ($specificUserId) {
            $user = User::find($specificUserId);
            if (!$user) {
                $this->error("User with ID {$specificUserId} not found.");
                return;
            }
        } else {
            $user = User::inRandomOrder()->first();
        }

        // Get order status
        if ($specificStatus) {
            $orderStatus = OrderStatus::where('name', $specificStatus)
                ->orWhere('slug', $specificStatus)
                ->orWhere('id', $specificStatus)
                ->first();
            if (!$orderStatus) {
                $this->error("Order status '{$specificStatus}' not found.");
                return;
            }
        } else {
            $orderStatus = OrderStatus::inRandomOrder()->first();
        }

        // Determine created date
        $createdAt = $recentOnly
            ? $this->faker->dateTimeBetween('-1 month', 'now')
            : $this->faker->dateTimeBetween('-6 months', 'now');

        // Create order without factory
        $order = new Order();
        $order->customer_id = $user->id;
        $order->status = $orderStatus->id;
        $order->display_id = $this->generateDisplayId();
        $order->tracking_number = $withTracking
            ? $this->faker->regexify('[A-Z]{2}[0-9]{10}')
            : $this->faker->optional(0.7)->regexify('[A-Z]{2}[0-9]{10}');
        $order->amount = 0; // Will be calculated after adding products
        $order->sales_tax = 0;
        $order->total = 0; // Will be calculated after adding products
        $order->paid_total = 0;
        $order->customer_name = $user->firstname . ' ' . $user->lastname;
        $order->customer_contact = $user->phone ?? $this->faker->phoneNumber();
        $order->customer_email = $user->email;
        $order->type = $this->faker->randomElement(['online', 'in-store', 'phone']);
        $order->require_shipping = $this->faker->boolean(80);
        $order->payment_gateway = $this->faker->randomElement(['stripe', 'paypal', 'square', 'cash']);
        $order->shipping_address = $this->generateShippingAddress();
        $order->billing_address = $this->generateBillingAddress();
        $order->delivery_fee = $this->faker->randomFloat(2, 0, 25);
        $order->items_weight = $this->faker->randomFloat(3, 0.1, 10);
        $order->created_at = $createdAt;
        $order->updated_at = $this->faker->dateTimeBetween($createdAt, 'now');
        $order->save();

        // Add random products to order
        $this->addRandomProductsToOrder($order, $minProducts, $maxProducts, $categories);

        // Calculate and update order totals
        $this->calculateOrderTotals($order);
    }

    /**
     * Generate a unique display ID for the order
     *
     * @return string
     */
    protected function generateDisplayId(): string
    {
        do {
            $displayId = 'ORD-' . strtoupper($this->faker->bothify('??###'));
        } while (Order::where('display_id', $displayId)->exists());

        return $displayId;
    }

    /**
     * Generate shipping address
     *
     * @return array
     */
    protected function generateShippingAddress(): array
    {
        return [
            'first_name' => $this->faker->firstName,
            'last_name' => $this->faker->lastName,
            'company' => $this->faker->optional(0.3)->company,
            'street_address' => $this->faker->streetAddress,
            'city' => $this->faker->city,
            'state' => $this->faker->state,
            'zip' => $this->faker->postcode,
            'country' => $this->faker->country,
            'phone' => $this->faker->phoneNumber,
        ];
    }

    /**
     * Generate billing address
     *
     * @return array
     */
    protected function generateBillingAddress(): array
    {
        // 70% chance billing address is same as shipping
        if ($this->faker->boolean(70)) {
            return $this->generateShippingAddress();
        }

        return [
            'first_name' => $this->faker->firstName,
            'last_name' => $this->faker->lastName,
            'company' => $this->faker->optional(0.3)->company,
            'street_address' => $this->faker->streetAddress,
            'city' => $this->faker->city,
            'state' => $this->faker->state,
            'zip' => $this->faker->postcode,
            'country' => $this->faker->country,
            'phone' => $this->faker->phoneNumber,
        ];
    }

    /**
     * Add random products to order
     *
     * @param Order $order
     * @param int $minProducts
     * @param int $maxProducts
     * @param array $categories
     * @return void
     */
    protected function addRandomProductsToOrder(Order $order, int $minProducts, int $maxProducts, array $categories = []): void
    {
        $productCount = $this->faker->numberBetween($minProducts, $maxProducts);

        // Build product query
        $productQuery = Product::query();

        // Filter by categories if specified
        if (!empty($categories)) {
            $productQuery->whereHas('category', function ($query) use ($categories) {
                $query->whereIn('name', $categories)
                    ->orWhereIn('slug', $categories);
            });
        }

        // Get random products (ensure no duplicates)
        $products = $productQuery->inRandomOrder()
            ->limit($productCount * 2) // Get more than needed to avoid duplicates
            ->get()
            ->unique('id')
            ->take($productCount);

        // If no products found with category filter, fall back to all products
        if ($products->isEmpty() && !empty($categories)) {
            $this->warn("No products found in specified categories. Using all products.");
            $products = Product::inRandomOrder()
                ->limit($productCount * 2)
                ->get()
                ->unique('id')
                ->take($productCount);
        }

        foreach ($products as $product) {
            $quantity = $this->faker->numberBetween(1, 5);
            $unitPrice = $product->price ?? $this->faker->randomFloat(2, 10, 500);
            $subtotal = $quantity * $unitPrice;

            // Create OrderProduct without using factory to avoid ID conflicts
            $orderProduct = new OrderProduct();
            $orderProduct->order_id = $order->id;
            $orderProduct->product_id = $product->id;
            $orderProduct->name = $product->name;
            $orderProduct->sku = $product->sku ?? $this->faker->bothify('SKU-###??');
            $orderProduct->barcode = $product->barcode ?? $this->faker->ean13();
            $orderProduct->order_quantity = $quantity;
            $orderProduct->unit_price = $unitPrice;
            $orderProduct->subtotal = $subtotal;



            $orderProduct->save();
        }
    }

    /**
     * Calculate and update order totals
     *
     * @param Order $order
     * @return void
     */
    protected function calculateOrderTotals(Order $order): void
    {
        $amount = $order->orderProducts()->sum('subtotal');
        $taxRate = $this->faker->randomFloat(4, 0.05, 0.15); // 5% to 15% tax
        $salesTax = $amount * $taxRate;
        $deliveryFee = $order->delivery_fee ?? 0;

        $total = $amount + $salesTax + $deliveryFee;
        $paidTotal = $this->faker->boolean(80) ? $total : $this->faker->randomFloat(2, 0, $total);

        $order->update([
            'amount' => $amount,
            'sales_tax' => $salesTax,
            'total' => $total,
            'paid_total' => $paidTotal,
        ]);
    }
}
