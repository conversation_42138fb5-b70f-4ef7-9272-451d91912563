<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class OrderProduct extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'order_product';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'product_id',
        'inventory_id',
        'variation_option_id',
        'name',
        'sku',
        'barcode',
        'width',
        'height',
        'length',
        'weight',
        'image',
        'banner',
        'order_quantity',
        'invoiced_quantity',
        'shipped_quantity',
        'canceled_quantity',
        'refunded_quantity',
        'unit_price',
        'upfront_amount',
        'discount_rate_type',
        'discount_rate',
        'discount',
        'store_credit',
        'gift_card_credit',
        'subtotal',
        'tax',
        'is_commissioned',
        'is_deposit',
        'product_data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'width' => 'decimal:2',
        'height' => 'decimal:2',
        'length' => 'decimal:2',
        'weight' => 'decimal:2',
        'order_quantity' => 'integer',
        'invoiced_quantity' => 'integer',
        'shipped_quantity' => 'integer',
        'canceled_quantity' => 'integer',
        'refunded_quantity' => 'integer',
        'unit_price' => 'float',
        'upfront_amount' => 'float',
        'discount_rate' => 'float',
        'discount' => 'float',
        'store_credit' => 'float',
        'gift_card_credit' => 'float',
        'subtotal' => 'float',
        'tax' => 'decimal:2',
        'is_commissioned' => 'boolean',
        'is_deposit' => 'boolean',
        'product_data' => 'json',
    ];

    /**
     * Get the order that owns the order product.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the product.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
