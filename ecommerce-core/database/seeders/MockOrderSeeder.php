<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\User;
use App\Models\OrderStatus;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Faker\Factory as Faker;

class MockOrderSeeder extends Seeder
{
    /**
     * Faker instance
     *
     * @var \Faker\Generator
     */
    protected $faker;

    /**
     * Create a new seeder instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->faker = Faker::create();
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $this->command->info('Seeding mock orders...');

        // Check if required data exists
        if (!$this->validateRequiredData()) {
            return;
        }

        // Create 50 orders with random products
        $orderCount = 50;
        $this->command->getOutput()->progressStart($orderCount);

        DB::transaction(function () use ($orderCount) {
            for ($i = 0; $i < $orderCount; $i++) {
                $this->createMockOrder();
                $this->command->getOutput()->progressAdvance();
            }
        });

        $this->command->getOutput()->progressFinish();
        $this->command->info("Successfully created {$orderCount} mock orders!");
    }

    /**
     * Validate that required data exists
     *
     * @return bool
     */
    protected function validateRequiredData(): bool
    {
        $users = User::count();
        $products = Product::count();
        $orderStatuses = OrderStatus::count();

        if ($users === 0) {
            $this->command->error('No users found. Please create users first.');
            return false;
        }

        if ($products === 0) {
            $this->command->error('No products found. Please create products first.');
            return false;
        }

        if ($orderStatuses === 0) {
            $this->command->error('No order statuses found. Please create order statuses first.');
            return false;
        }

        $this->command->info("Found {$users} users, {$products} products, and {$orderStatuses} order statuses.");
        return true;
    }

    /**
     * Create a single mock order with random products
     *
     * @return void
     */
    protected function createMockOrder(): void
    {
        // Get random user and order status
        $user = User::inRandomOrder()->first();
        $orderStatus = OrderStatus::inRandomOrder()->first();

        // Create order without factory
        $order = new Order();
        $order->customer_id = $user->id;
        $order->status = $orderStatus->id;
        $order->display_id = $this->generateUniqueDisplayId();
        $order->tracking_number = $this->faker->optional(0.7)->regexify('[A-Z]{2}[0-9]{10}');
        $order->customer_name = $user->firstname . ' ' . $user->lastname;
        $order->customer_contact = $user->phone ?? $this->faker->phoneNumber();
        $order->customer_email = $user->email;
        $order->type = $this->faker->randomElement(['online', 'in-store', 'phone']);
        $order->require_shipping = $this->faker->boolean(80);
        $order->payment_gateway = $this->faker->randomElement(['stripe', 'paypal', 'square', 'cash']);
        $order->shipping_address = $this->generateAddress();
        $order->billing_address = $this->generateAddress();
        $order->delivery_fee = $this->faker->randomFloat(2, 0, 25);
        $order->items_weight = $this->faker->randomFloat(3, 0.1, 10);
        $order->amount = 0; // Will be calculated
        $order->sales_tax = 0; // Will be calculated
        $order->total = 0; // Will be calculated
        $order->paid_total = 0; // Will be calculated
        $order->created_at = $this->faker->dateTimeBetween('-6 months', 'now');
        $order->save();

        // Add 1-5 random products to the order
        $productCount = $this->faker->numberBetween(1, 5);
        $this->addRandomProductsToOrder($order, $productCount);

        // Calculate and update order totals
        $this->calculateOrderTotals($order);
    }

    /**
     * Generate a unique display ID for the order
     *
     * @return string
     */
    protected function generateUniqueDisplayId(): string
    {
        do {
            $displayId = 'ORD-' . strtoupper($this->faker->bothify('??###'));
        } while (Order::where('display_id', $displayId)->exists());

        return $displayId;
    }

    /**
     * Generate address data
     *
     * @return array
     */
    protected function generateAddress(): array
    {
        return [
            'first_name' => $this->faker->firstName,
            'last_name' => $this->faker->lastName,
            'company' => $this->faker->optional(0.3)->company,
            'street_address' => $this->faker->streetAddress,
            'apartment' => $this->faker->optional(0.4)->secondaryAddress,
            'city' => $this->faker->city,
            'state' => $this->faker->state,
            'zip' => $this->faker->postcode,
            'country' => $this->faker->country,
            'phone' => $this->faker->phoneNumber,
            'email' => $this->faker->optional(0.8)->email,
        ];
    }

    /**
     * Add random products to order
     *
     * @param Order $order
     * @param int $productCount
     * @return void
     */
    protected function addRandomProductsToOrder(Order $order, int $productCount): void
    {
        // Get random products (ensure no duplicates)
        $products = Product::inRandomOrder()
            ->limit($productCount * 2) // Get more than needed to avoid duplicates
            ->get()
            ->unique('id')
            ->take($productCount);

        foreach ($products as $product) {
            $quantity = $this->faker->numberBetween(1, 5);
            $unitPrice = $product->price ?? $this->faker->randomFloat(2, 10, 500);
            $subtotal = $quantity * $unitPrice;

            // Create OrderProduct without using factory to avoid ID conflicts
            $orderProduct = new OrderProduct();
            $orderProduct->order_id = $order->id;
            $orderProduct->product_id = $product->id;
            $orderProduct->name = $product->name;
            $orderProduct->sku = $product->sku ?? $this->faker->bothify('SKU-###??');
            $orderProduct->barcode = $product->barcode ?? $this->faker->ean13();
            $orderProduct->order_quantity = $quantity;
            $orderProduct->unit_price = $unitPrice;
            $orderProduct->subtotal = $subtotal;

            // Set product_data (Laravel will handle JSON casting automatically)
            $orderProduct->product_data = $this->generateProductData($product);
            $orderProduct->save();
        }
    }

    /**
     * Generate product data for order product
     *
     * @param Product $product
     * @return array
     */
    protected function generateProductData(Product $product): array
    {
        return [
            'name' => $product->name,
            'description' => $product->description ?? $this->faker->sentence(),
            'image' => $product->image ?? null,
            'category' => $product->category->name ?? 'General',
            'brand' => $product->brand ?? $this->faker->company(),
            'weight' => $this->faker->randomFloat(2, 0.1, 10),
            'dimensions' => [
                'length' => $this->faker->randomFloat(2, 1, 50),
                'width' => $this->faker->randomFloat(2, 1, 50),
                'height' => $this->faker->randomFloat(2, 1, 50),
                'unit' => 'cm',
            ],
            'color' => $this->faker->optional(0.6)->colorName(),
            'size' => $this->faker->optional(0.4)->randomElement(['XS', 'S', 'M', 'L', 'XL', 'XXL']),
            'material' => $this->faker->optional(0.5)->randomElement([
                'Cotton',
                'Polyester',
                'Leather',
                'Metal',
                'Plastic',
                'Wood'
            ]),
            'features' => $this->faker->optional(0.7)->words(3, true),
            'warranty' => $this->faker->optional(0.5)->randomElement([
                '1 year',
                '2 years',
                '3 years',
                '6 months',
                'Lifetime'
            ]),
        ];
    }

    /**
     * Calculate and update order totals
     *
     * @param Order $order
     * @return void
     */
    protected function calculateOrderTotals(Order $order): void
    {
        $amount = $order->orderProducts()->sum('subtotal');
        $taxRate = $this->faker->randomFloat(4, 0.05, 0.15); // 5% to 15% tax
        $salesTax = $amount * $taxRate;
        $deliveryFee = $order->delivery_fee ?? 0;

        $total = $amount + $salesTax + $deliveryFee;
        $paidTotal = $this->faker->boolean(80) ? $total : $this->faker->randomFloat(2, 0, $total);

        $order->update([
            'amount' => $amount,
            'sales_tax' => $salesTax,
            'total' => $total,
            'paid_total' => $paidTotal,
        ]);
    }

    /**
     * Create orders with specific scenarios for testing
     *
     * @return void
     */
    public function createTestScenarios(): void
    {
        $this->command->info('Creating test scenario orders...');

        // Scenario 1: Recent paid order with tracking
        $this->createScenarioOrder([
            'payment_status' => 'paid',
            'tracking_number' => $this->faker->regexify('[A-Z]{2}[0-9]{10}'),
            'created_at' => now()->subDays(2),
        ]);

        // Scenario 2: Pending order without tracking
        $this->createScenarioOrder([
            'payment_status' => 'pending',
            'tracking_number' => null,
            'created_at' => now()->subHours(6),
        ]);

        // Scenario 3: Large order with many products
        $this->createScenarioOrder([
            'payment_status' => 'paid',
            'product_count' => 8,
            'created_at' => now()->subDays(1),
        ]);

        // Scenario 4: Order with discount
        $this->createScenarioOrder([
            'payment_status' => 'paid',
            'with_discount' => true,
            'created_at' => now()->subDays(3),
        ]);

        $this->command->info('Test scenario orders created successfully!');
    }

    /**
     * Create order with specific scenario
     *
     * @param array $scenario
     * @return void
     */
    protected function createScenarioOrder(array $scenario): void
    {
        $user = User::inRandomOrder()->first();
        $orderStatus = OrderStatus::inRandomOrder()->first();

        // Create order without factory
        $order = new Order();
        $order->customer_id = $user->id;
        $order->status = $orderStatus->id;
        $order->display_id = $this->generateUniqueDisplayId();
        $order->customer_name = $user->firstname . ' ' . $user->lastname;
        $order->customer_contact = $user->phone ?? $this->faker->phoneNumber();
        $order->customer_email = $user->email;
        $order->type = $this->faker->randomElement(['online', 'in-store', 'phone']);
        $order->require_shipping = $this->faker->boolean(80);
        $order->payment_gateway = $this->faker->randomElement(['stripe', 'paypal', 'square', 'cash']);
        $order->shipping_address = $this->generateAddress();
        $order->billing_address = $this->generateAddress();
        $order->delivery_fee = $this->faker->randomFloat(2, 0, 25);
        $order->items_weight = $this->faker->randomFloat(3, 0.1, 10);
        $order->amount = 0;
        $order->sales_tax = 0;
        $order->total = 0;
        $order->paid_total = 0;

        // Apply scenario-specific data
        if (isset($scenario['tracking_number'])) {
            $order->tracking_number = $scenario['tracking_number'];
        } else {
            $order->tracking_number = $this->faker->optional(0.7)->regexify('[A-Z]{2}[0-9]{10}');
        }

        if (isset($scenario['created_at'])) {
            $order->created_at = $scenario['created_at'];
        } else {
            $order->created_at = $this->faker->dateTimeBetween('-6 months', 'now');
        }

        $order->save();

        // Add products
        $productCount = $scenario['product_count'] ?? $this->faker->numberBetween(2, 4);
        $this->addRandomProductsToOrder($order, $productCount);

        // Calculate totals
        $this->calculateOrderTotals($order);
    }
}
