import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useRouter } from 'next/router';
import {
  useOrderQuery,
  getOrderStatusColor,
  formatOrderStatus,
} from '../../data-graphql/orders';
import {
  fetchProducts,
  useProductsQuery,
  ProductsQueryVariables,
} from '../../data-graphql/products';

import { LoadingContainer } from '@components/ui/loaders';
import MobileHeader from '@components/ui/mobile-header';
import { toast } from 'react-toastify';
import {
  CapacitorBarcodeScanner,
  CapacitorBarcodeScannerTypeHint,
} from '@capacitor/barcode-scanner';
import PriceRenderer from '@components/common/price-renderer';
import { MdEdit, MdAdd, MdCheck } from 'react-icons/md';
import { playSuccessSound, playErrorSound } from '@utils/audio-feedback';
import Button from '@components/ui/button';
import FloatingActionButton, {
  FabMenuItem,
} from '@components/ui/floating-action-button';
import Input from '@components/ui/input';
import { useForm } from 'react-hook-form';
import { eq } from '@utils/graphql-query-builder';
import { ProductColumn } from 'src/data-graphql/graphql-columns';
import { BiBarcode } from 'react-icons/bi';

interface CustomProductFormData {
  name: string;
  barcode: string;
  price: number;
  quantity: number;
}

const OrderDetailsPage = () => {
  const router = useRouter();
  const { orderId } = router.query;
  const [scannedProducts, setScannedProducts] = useState<Map<string, any>>(
    new Map()
  );
  console.log('scannedProducts', scannedProducts);
  const [scanningProduct, setScanningProduct] = useState<string | null>(null);

  // Custom product states
  const [scanningCustomProduct, setScanningCustomProduct] = useState(false);
  const [showCustomProductForm, setShowCustomProductForm] = useState(false);
  const [customProductMode, setCustomProductMode] = useState<'scan' | 'manual'>(
    'manual'
  );
  const [customProducts, setCustomProducts] = useState<any[]>([]);

  // Ref for scrolling to custom product form
  const customProductFormRef = useRef<HTMLDivElement>(null);

  // Scroll to custom product form when it's shown
  useEffect(() => {
    if (showCustomProductForm && customProductFormRef.current) {
      setTimeout(() => {
        customProductFormRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }, 100); // Small delay to ensure form is rendered
    }
  }, [showCustomProductForm]);

  // Helper function to validate product by barcode
  const validateProductByBarcode = async (barcode: string) => {
    try {
      const productsResponse = await fetchProducts({
        where: eq(ProductColumn.BARCODE, barcode),
      });
      const products = productsResponse.products.data;

      if (products && products.length > 0) {
        return products[0]; // Return the found product
      }
      return null; // Product not found
    } catch (error) {
      console.error('Product validation error:', error);
      throw error;
    }
  };

  // React Hook Form setup
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<CustomProductFormData>({
    defaultValues: {
      name: '',
      barcode: '',
      price: 0,
      quantity: 1,
    },
  });

  // Validate barcode
  const validateBarcode = (
    scannedCode: string,
    expectedBarcode: string
  ): boolean => {
    return scannedCode === expectedBarcode;
  };

  // Handle camera scanning
  const handleCameraScan = async (product: any) => {
    const expectedBarcode =
      (product.product as any)?.barcode || product.barcode;

    if (!expectedBarcode) {
      toast.error('No barcode available for this product');
      return;
    }

    try {
      setScanningProduct(product.id);

      const result = await CapacitorBarcodeScanner.scanBarcode({
        hint: CapacitorBarcodeScannerTypeHint.ALL,
        scanInstructions: `Scan barcode for: ${product.name}`,
        scanButton: false,
        scanText: 'Scan',
        cameraDirection: 1, // BACK camera
        scanOrientation: 3, // ADAPTIVE
      });

      if (result.ScanResult) {
        const isValid = validateBarcode(result.ScanResult, expectedBarcode);

        if (isValid) {
          playSuccessSound();
          setScannedProducts((prev) => {
            const newMap = new Map(prev);
            const existingProduct = newMap.get(expectedBarcode);
            if (existingProduct) {
              newMap.set(expectedBarcode, {
                ...existingProduct,
                isScanned: true,
              });
            }
            return newMap;
          });
          toast.success(`✅ ${product.name} scanned successfully!`);
        } else {
          playErrorSound();
          toast.error(
            `❌ Barcode mismatch!\nExpected: ${expectedBarcode}\nScanned: ${result.ScanResult}`
          );
        }
      }
    } catch (err) {
      console.error('Barcode scan error:', err);
      playErrorSound();
      toast.error('Failed to scan barcode. Please try again.');
    } finally {
      setScanningProduct(null);
    }
  };

  // Handle manual input
  const handleManualInput = (product: any) => {
    const expectedBarcode =
      (product.product as any)?.barcode || product.barcode;

    if (!expectedBarcode) {
      toast.error('No barcode available for this product');
      return;
    }

    const code = prompt(
      `Enter barcode for ${product.name}:\nExpected: ${expectedBarcode}`
    );
    if (code) {
      const isValid = validateBarcode(code, expectedBarcode);

      if (isValid) {
        playSuccessSound();
        setScannedProducts((prev) => {
          const newMap = new Map(prev);
          const existingProduct = newMap.get(expectedBarcode);
          if (existingProduct) {
            newMap.set(expectedBarcode, {
              ...existingProduct,
              isScanned: true,
            });
          }
          return newMap;
        });
        toast.success(`✅ ${product.name} verified successfully!`);
      } else {
        playErrorSound();
        toast.error(
          `❌ Barcode mismatch!\nExpected: ${expectedBarcode}\nEntered: ${code}`
        );
      }
    }
  };

  // Custom product handlers
  const handleScanCustomProduct = async () => {
    console.log('handleScanCustomProduct called'); // Debug log
    try {
      setScanningCustomProduct(true);
      setCustomProductMode('scan');

      const result = await CapacitorBarcodeScanner.scanBarcode({
        hint: CapacitorBarcodeScannerTypeHint.ALL,
        scanInstructions: 'Scan product barcode',
        scanButton: false,
        scanText: 'Scan',
        cameraDirection: 1,
        scanOrientation: 3,
      });

      if (result.ScanResult) {
        console.log('Barcode scanned:', result.ScanResult);

        // Validate barcode against products database
        try {
          const product = await validateProductByBarcode(result.ScanResult);

          if (product) {
            // Product found - prefill form with product data
            reset({
              name: product.name,
              barcode: product.barcode || result.ScanResult,
              price: product.price || 0,
              quantity: 1,
            });
            setShowCustomProductForm(true);
            toast.success(`✅ Product found: ${product.name}`);
          } else {
            // Product not found - show error
            toast.error(
              `❌ Product with barcode "${result.ScanResult}" not found in database.`
            );
            playErrorSound();
            return;
          }
        } catch (validationError) {
          console.error('Product validation error:', validationError);
          toast.error('Failed to validate product. Please try again.');
          playErrorSound();
          return;
        }
      }
    } catch (error) {
      console.error('Barcode scan error:', error);
      toast.error('Failed to scan barcode. Please try again.');
      playErrorSound();
    } finally {
      setScanningCustomProduct(false);
    }
  };

  const handleManualCustomProduct = async () => {
    console.log('handleManualCustomProduct called'); // Debug log

    // Show prompt to enter barcode for validation
    const barcode = window.prompt(
      'Please enter the barcode to validate and add as custom product:'
    );

    if (!barcode || !barcode.trim()) {
      return; // User cancelled or entered empty barcode
    }

    try {
      const product = await validateProductByBarcode(barcode.trim());

      if (product) {
        // Product found - prefill form with product data
        setCustomProductMode('manual');
        reset({
          name: product.name,
          barcode: product.barcode || barcode.trim(),
          price: product.price || 0,
          quantity: 1,
        });
        setShowCustomProductForm(true);
        toast.success(`✅ Product found: ${product.name}`);
        playSuccessSound();
      } else {
        // Product not found - show form with barcode for manual entry
        setCustomProductMode('manual');
        reset({
          name: '',
          barcode: barcode.trim(),
          price: 0,
          quantity: 1,
        });
        setShowCustomProductForm(true);
        toast.info(
          `Product with barcode "${barcode.trim()}" not found. Please enter product details manually.`
        );
      }
    } catch (error) {
      console.error('Product validation error:', error);
      toast.error('Failed to validate product. Please try again.');
      playErrorSound();
    }
  };

  const handleCancelCustomProduct = () => {
    setShowCustomProductForm(false);
    setCustomProductMode('manual');
    reset({
      name: '',
      barcode: '',
      price: 0,
      quantity: 1,
    });
  };

  // Form submission handler
  const onSubmitCustomProduct = async (data: CustomProductFormData) => {
    try {
      // First validate the barcode to get the actual product data
      const barcode = data.barcode.trim();
      if (!barcode) {
        toast.error('Barcode is required');
        return;
      }

      let productFromDB = null;
      try {
        productFromDB = await validateProductByBarcode(barcode);
      } catch (error) {
        console.error('Error validating product:', error);
      }

      // Use actual product data if found, otherwise create custom product structure
      const orderProductId = productFromDB?.id || `CUSTOM-${Date.now()}`;
      const productId = productFromDB?.id || `CUSTOM-${Date.now()}`;

      // Structure product to match order product format
      const orderProduct = {
        id: orderProductId,
        name: productFromDB?.name || data.name.trim(),
        sku: productFromDB?.sku || productId,
        barcode: barcode,
        order_quantity: data.quantity,
        invoiced_quantity: 0,
        shipped_quantity: 0,
        canceled_quantity: 0,
        refunded_quantity: 0,
        unit_price: productFromDB?.price || data.price,
        discount: 0,
        subtotal: (productFromDB?.price || data.price) * data.quantity,
        tax: 0,
        product: {
          id: productId,
          name: productFromDB?.name || data.name.trim(),
          slug:
            productFromDB?.slug ||
            data.name.trim().toLowerCase().replace(/\s+/g, '-'),
          description: productFromDB?.description || null,
          image: productFromDB?.image || '',
          gallery: productFromDB?.gallery || [],
          barcode: barcode,
        },
        isCustom: !productFromDB, // Only custom if not found in database
        orderId: orderId,
      };

      // TODO: Implement API call to add product to order
      console.log('Adding product to order payload:', orderProduct);

      setScannedProducts(
        (prev) =>
          new Map([
            ...prev,
            [
              barcode,
              {
                ...orderProduct,
                isScanned: true,
              },
            ],
          ])
      );

      const productType = productFromDB ? 'Product' : 'Custom product';
      toast.success(
        `✅ ${productType} "${orderProduct.name}" added to order and verified!`
      );
      playSuccessSound();

      // Reset form and close
      handleCancelCustomProduct();

      // Optionally refresh the order data here
      // router.reload(); // or refetch order data
    } catch (error) {
      console.error('Error adding product:', error);
      toast.error('Failed to add product. Please try again.');
      playErrorSound();
    }
  };

  // Handle manual barcode validation
  const handleValidateBarcode = async () => {
    // Show prompt to enter barcode
    const barcode = window.prompt(
      'Please enter the barcode to validate:',
      watch('barcode') || ''
    );

    if (!barcode || !barcode.trim()) {
      return; // User cancelled or entered empty barcode
    }

    try {
      const product = await validateProductByBarcode(barcode.trim());

      if (product) {
        // Product found - prefill form with product data
        setValue('barcode', barcode.trim());
        setValue('name', product.name);
        setValue('price', product.price || 0);
        toast.success(`✅ Product found: ${product.name}`);
        playSuccessSound();
      } else {
        // Product not found - show error
        toast.error(
          `❌ Product with barcode "${barcode.trim()}" not found in database.`
        );
        playErrorSound();
      }
    } catch (error) {
      console.error('Product validation error:', error);
      toast.error('Failed to validate product. Please try again.');
      playErrorSound();
    }
  };

  // FAB menu items configuration
  const fabMenuItems: FabMenuItem[] = [
    {
      id: 'scan-barcode',
      label: 'Scan Barcode',
      description: 'Use camera to scan product',
      icon: <BiBarcode className="w-5 h-5" />,
      onClick: handleScanCustomProduct,
      loading: scanningCustomProduct,
      disabled: scanningCustomProduct,
      color: 'blue',
    },
    {
      id: 'manual-entry',
      label: 'Manual Entry',
      description: 'Enter product details manually',
      icon: <MdAdd className="w-5 h-5" />,
      onClick: handleManualCustomProduct,
      color: 'green',
    },
  ];

  // Fetch order details
  const {
    data: orderData,
    isLoading: orderLoading,
    error: orderError,
  } = useOrderQuery(orderId as string, {
    enabled: !!orderId,
  });

  // Initialize scanned products when order is loaded
  const initializedScannedProducts = useMemo(() => {
    if (!orderData?.order?.order_products) return new Map();

    const productMap = new Map();
    orderData.order.order_products.forEach((product) => {
      const barcode = (product.product as any)?.barcode || product.barcode;
      if (barcode) {
        productMap.set(barcode, {
          ...product,
          isScanned: false,
        });
      }
    });
    return productMap;
  }, [orderData?.order?.order_products]);

  // Update scannedProducts when order is loaded
  useEffect(() => {
    if (initializedScannedProducts.size > 0 && scannedProducts.size === 0) {
      setScannedProducts(initializedScannedProducts);
    }
  }, [initializedScannedProducts, scannedProducts.size]);

  // Check if all products are verified/scanned (must be before early returns)
  const allProductsVerified = useMemo(() => {
    if (!orderData?.order?.order_products) return false;
    const totalProducts = orderData.order.order_products.length;
    const scannedCount = Array.from(scannedProducts.values()).filter(
      (product) => product.isScanned
    ).length;
    return totalProducts > 0 && scannedCount === totalProducts;
  }, [orderData?.order?.order_products?.length, scannedProducts]);

  if (orderLoading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <LoadingContainer
          loading={true}
          text="Loading order details..."
          minHeight="100vh"
        >
          <div></div>
        </LoadingContainer>
      </div>
    );
  }

  if (orderError) {
    return (
      <div className="min-h-screen bg-gray-100">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-red-600">Error loading order details.</p>
            <Button
              onClick={() => router.back()}
              variant="border"
              className="mt-4"
            >
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!orderData?.order) {
    return (
      <div className="min-h-screen bg-gray-100">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-gray-600">Order not found.</p>
            <Button
              onClick={() => router.back()}
              variant="border"
              className="mt-4"
            >
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const order = orderData.order;

  return (
    <div className="min-h-screen bg-gray-100">
      <MobileHeader
        title={`Order ${order.display_id || `#${order.id}`}`}
        rightComponent={
          <div className="flex flex-col items-end space-y-1">
            {/* Progress indicator */}
            <div className="text-xs text-gray-600">
              {
                Array.from(scannedProducts.values()).filter((p) => p.isScanned)
                  .length
              }
              /{order?.order_products?.length || 0} verified
            </div>
            {/* Process order button */}
            <Button
              disabled={!allProductsVerified}
              onClick={() =>
                router.push(`/orders/${order.id}/shipments/create`)
              }
            >
              Process Order
            </Button>
          </div>
        }
      />

      <div className="px-4 py-6 space-y-6">
        {/* Order Summary */}
        <div className="bg-white rounded-lg shadow-sm p-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Order Summary
          </h2>
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Order ID:</span>
              <span className="font-medium">
                {order.display_id || `#${order.id}`}
              </span>
            </div>
            {order.tracking_number && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Tracking Number:</span>
                <span className="font-medium">{order.tracking_number}</span>
              </div>
            )}
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Customer:</span>
              <span className="font-medium">
                {order.customer_name || order.customer?.name || 'N/A'}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Date:</span>
              <span className="font-medium">
                {new Date(order.created_at).toLocaleDateString()} at{' '}
                {new Date(order.created_at).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Payment Method:</span>
              <span className="font-medium">
                {order.payment_gateway || 'N/A'}
              </span>
            </div>
            {order.delivery_fee && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Delivery Fee:</span>
                <span className="font-medium">
                  <PriceRenderer price={order.delivery_fee} />
                </span>
              </div>
            )}
            <div className="border-t pt-3">
              <div className="flex justify-between text-lg font-semibold">
                <span>Total:</span>
                <span className="text-green-600">
                  <PriceRenderer price={order.total} />
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Order Products */}
        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                Order Items ({Array.from(scannedProducts.values()).length})
              </h2>
              {/* Scanning Progress */}
              <div className="flex items-center space-x-2 text-sm mt-1">
                <span className="text-gray-600">
                  Scanned:{' '}
                  {
                    Array.from(scannedProducts.values()).filter(
                      (p) => p.isScanned
                    ).length
                  }
                  /{Array.from(scannedProducts.values()).length}
                </span>
                {Array.from(scannedProducts.values()).filter((p) => p.isScanned)
                  .length === (order.order_products?.length || 0) &&
                  (order.order_products?.length || 0) > 0 && (
                    <span className="flex items-center space-x-1 bg-green-100 text-green-700 px-2 py-1 rounded text-xs">
                      <MdCheck className="w-3 h-3" />
                      <span>All Scanned</span>
                    </span>
                  )}
              </div>
            </div>
          </div>

          {Array.from(scannedProducts.values()).length > 0 ? (
            <div className="space-y-4">
              {Array.from(scannedProducts.values()).map((product) => (
                <div key={product.id} className="border rounded-lg p-4">
                  <div className="flex items-start space-x-4">
                    {product.product?.image && (
                      <img
                        src={product.product.image}
                        alt={product.name}
                        className="w-16 h-16 object-cover rounded-lg border"
                      />
                    )}
                    <div className="flex-1">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900">
                            {product.name}
                          </h3>
                          {product.sku && (
                            <p className="text-sm text-gray-600">
                              SKU: {product.sku}
                            </p>
                          )}
                          {/* Show product barcode and scanning controls */}
                          {((product.product as any)?.barcode ||
                            product.barcode) && (
                            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                              <p className="text-sm text-gray-600 mb-3">
                                Barcode:{' '}
                                {(product.product as any)?.barcode ||
                                  product.barcode}
                              </p>

                              {product.isScanned ? (
                                <div className="flex items-center space-x-2 bg-green-100 text-green-700 px-3 py-2 rounded-lg">
                                  <MdCheck className="w-4 h-4" />
                                  <span className="font-medium">Verified</span>
                                </div>
                              ) : (
                                <div className="flex items-center space-x-2">
                                  {/* Camera Scan Button - Larger */}
                                  <Button
                                    onClick={() => handleCameraScan(product)}
                                    disabled={scanningProduct === product.id}
                                    className="flex items-center space-x-2 flex-1"
                                    title="Scan with camera"
                                    loading={scanningProduct === product.id}
                                  >
                                    {scanningProduct === product.id ? (
                                      <span className="font-medium">
                                        Scanning...
                                      </span>
                                    ) : (
                                      <>
                                        <BiBarcode className="w-5 h-5" />
                                        <span className="font-medium">
                                          Scan
                                        </span>
                                      </>
                                    )}
                                  </Button>

                                  {/* Manual Input Button - Smaller */}
                                  <Button
                                    onClick={() => handleManualInput(product)}
                                    variant="border"
                                    className="flex items-center space-x-1 px-3"
                                    title="Manual input"
                                  >
                                    <MdEdit className="w-4 h-4" />
                                  </Button>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="mt-2 flex items-center justify-between">
                        <div className="text-sm text-gray-600">
                          Qty: {product.order_quantity} ×{' '}
                          <PriceRenderer price={product.unit_price} />
                        </div>
                        <div className="font-semibold text-green-600">
                          <PriceRenderer price={product.subtotal} />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-600">No products found in this order.</p>
            </div>
          )}
        </div>

        {/* Custom Product Form Modal/Section */}
        {showCustomProductForm && (
          <div
            ref={customProductFormRef}
            className="bg-white rounded-lg shadow-sm p-6 mt-4 border-2 border-blue-200"
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">
                {customProductMode === 'scan'
                  ? 'Scanned Product'
                  : 'Add Custom Product'}
              </h4>
            </div>

            <form
              onSubmit={handleSubmit(onSubmitCustomProduct)}
              className="space-y-4"
            >
              {/* Product Name */}
              <Input
                label="Product Name *"
                placeholder="Enter product name"
                {...register('name', {
                  required: 'Product name is required',
                  minLength: {
                    value: 2,
                    message: 'Product name must be at least 2 characters',
                  },
                })}
                error={errors.name?.message}
                variant="solid"
              />

              {/* Product Barcode/SKU */}
              <div>
                <Input
                  label="Barcode/SKU"
                  placeholder={
                    customProductMode === 'scan'
                      ? 'Scanned barcode will appear here'
                      : 'Enter barcode or SKU'
                  }
                  {...register('barcode')}
                  error={errors.barcode?.message}
                  variant="solid"
                  readOnly={customProductMode === 'scan'}
                />
                {customProductMode === 'manual' && (
                  <Button
                    type="button"
                    onClick={handleValidateBarcode}
                    variant="border"
                    className="mt-2 text-sm"
                    disabled={isSubmitting}
                  >
                    Validate Product
                  </Button>
                )}
              </div>

              {/* Price */}
              <div>
                <Input
                  label="Price"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  {...register('price', {
                    valueAsNumber: true,
                    min: {
                      value: 0,
                      message: 'Price must be 0 or greater',
                    },
                  })}
                  error={errors.price?.message}
                  variant="solid"
                  inputClassName="pl-8"
                />
              </div>

              {/* Quantity */}
              <Input
                label="Quantity *"
                type="number"
                min="1"
                placeholder="1"
                {...register('quantity', {
                  valueAsNumber: true,
                  required: 'Quantity is required',
                  min: {
                    value: 1,
                    message: 'Quantity must be at least 1',
                  },
                })}
                error={errors.quantity?.message}
                variant="solid"
              />

              {/* Action Buttons */}
              <div className="flex space-x-3 pt-4">
                <Button
                  type="button"
                  onClick={handleCancelCustomProduct}
                  variant="border"
                  className="flex-1"
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  loading={isSubmitting}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Adding...' : 'Add to Order'}
                </Button>
              </div>
            </form>
          </div>
        )}

        {/* Floating Action Button */}
        <FloatingActionButton
          menuItems={fabMenuItems}
          size="md"
          color="blue"
          tooltip="Add Custom Product"
        />
      </div>
    </div>
  );
};

export default OrderDetailsPage;
